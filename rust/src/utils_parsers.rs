use std::collections::HashMap;
use regex::Regex;
use once_cell::sync::Lazy;

use crate::utils::{
    normalize_date,
    normalize_dns_question_name,
    normalize_time,
};

use crate::utils_classes::{
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
    MYSQLValue,
};

use crate::utils_patterns::{
    DAEMON_PATTERN,
    DHCP_PATTERN,
    DHCP_REST_PATTERN,
    DNS_PATTERN,
    DNS_REST_PATTERN,
    FILTERLOG_PATTERN,
    ROUTER_PATTERN,
    ROUTERBOARD_PATTERN,
    SNORT_PATTERN,
    SQUID_PATTERN,
    SWITCH_PATTERN,
    USERAUDIT_PATTERN,
    USERNOTICE_PATTERN,
    USERWARNING_PATTERN,
    VMWARE_PATTERN,
    VPNSERVER_PATTERN,
    WINDOWSSERVER_PATTERN,
    WS_AN_AD_PATTERN,
    WS_SW_PATTERN,
};

#[derive(Debug, Clone)]
pub enum ConfigType {
    Daemon,
    DHCP,
    DNS,
    FilterLog,
    Router,
    RouterBoard,
    Snort,
    Squid,
    Switch,
    UserAudit,
    UserNotice,
    UserWarning,
    VMware,
    VPNServer,
    WindowsServer,
}

// added pub so that we can run tests in test_utils.rs
pub fn _is_invalid_ln(ln: &str) -> bool {
    if ln.is_empty() ||
       ln.contains("ERROR name exceeds safe print buffer length") ||
       ln.contains("ERROR length byte") ||
       ln.contains("leads outside message") ||
       ln.contains("Exiting on signal") ||
       ln.contains("Now monitoring attacks") ||
       ln.contains("spp_arpspoof") ||
       ln.contains("because it is a directory, not a file") ||
       !ln.chars().next().unwrap_or(' ').is_ascii_digit() {
        return true;
    }
    false
}

fn _invalid_line_sections(
    object_list_of_names_and_addresses: &[String],
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
    line_object: &str,
    line_event_type: &str,
    line_alert_type: &str,
) -> bool {
    if event_types.is_empty() && filterby.is_empty() && line_object.is_empty() {
        return false;
    }

    if !line_object.is_empty() && !object_list_of_names_and_addresses.contains(&line_object.to_string()) {
        return true;
    }

    if !event_types.is_empty() && !event_types.contains(&line_event_type.to_string()) {
        return true;
    }

    if filterby_is_in_alert_type && !line_alert_type.contains(filterby) {
        return true;
    }

    false
}

fn _extract_matches_from_pattern(string: &str, pattern: &Regex) -> Option<Vec<String>> {
    pattern.captures(string).map(|caps| {
        caps.iter()
            .skip(1)
            .map(|m| m.map_or("".to_string(), |m| m.as_str().to_string()))
            .collect()
    })
}

fn _parse_daemon(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &DAEMON_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_sensor = object_dict_of_addresses_and_names
        .get(line_sensor)
        .unwrap_or(line_sensor)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),
    ];

    (Some(line_sensor), Some(row))
}

fn _parse_dhcp(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if ln.contains("ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern(ln, &DHCP_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    // let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];
    let rest            = &splited[5];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let rest_splited = match _extract_matches_from_pattern(rest, &DHCP_REST_PATTERN) {
        Some(r_s) => r_s,
        None => return (None, None),
    };

    let row = vec![
        normalize_date(&rest_splited[1]),  // __TODO__ why no .to_string() ?
        rest_splited[2].clone(),
        rest_splited[0].clone(),
        rest_splited[3].clone(),
        rest_splited[4].clone(),
        rest_splited[5].clone(),
        rest_splited[6].clone(),
        rest_splited[7].clone(),
        rest_splited[8].clone(),
        rest_splited[9].clone(),
        rest_splited[10].clone(),
        rest_splited[11].clone(),
        rest_splited[12].clone(),
        rest_splited[13].clone(),
        rest_splited[14].clone(),
        rest_splited[15].clone(),
        rest_splited[16].clone(),
        rest_splited[17].clone(),
        rest_splited[18].clone(),
    ];

    (None, Some(row))
}

fn _parse_dns(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &DNS_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    // let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];
    let rest            = &splited[5];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let rest_splited = match _extract_matches_from_pattern(rest, &DNS_REST_PATTERN) {
        Some(r_s) => r_s,
        None => return (None, None),
    };

    let row = vec![
        normalize_date(&rest_splited[0]),
        normalize_time(&format!("{} {}", rest_splited[1], rest_splited[2])),
        rest_splited[3].clone(),
        rest_splited[4].clone(),
        rest_splited[5].clone(),
        rest_splited[6].clone(),
        rest_splited[7].clone(),
        rest_splited[8].clone(),
        rest_splited[9].clone(),
        rest_splited[10].clone(),
        rest_splited[11].clone(),
        rest_splited[12].clone(),
        rest_splited[13].clone(),
        rest_splited[14].clone(),
        rest_splited[15].clone(),
        normalize_dns_question_name(&rest_splited[16]).to_lowercase(),
    ];

    (None, Some(row))
}

fn _parse_router(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &ROUTER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_router     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_router,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_router = object_dict_of_addresses_and_names
        .get(line_router)
        .unwrap_or(line_router)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),
    ];

    (Some(line_router), Some(row))
}

fn _parse_routerboard(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &ROUTERBOARD_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_routerboard = &splited[2];
    let line_event_type  = &splited[3];
    let line_alert_type  = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_routerboard,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_routerboard = object_dict_of_addresses_and_names
        .get(line_routerboard)
        .unwrap_or(line_routerboard)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),
    ];

    (Some(line_routerboard), Some(row))
}

fn _parse_snort(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &SNORT_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_sensor = object_dict_of_addresses_and_names
        .get(line_sensor)
        .unwrap_or(line_sensor)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        splited[5].clone(),
        splited[6].clone(),
        splited[7].clone(),
        splited[8].clone(),
        splited[9].clone(),
        splited[10].clone(),
        splited[11].clone(),
        splited[12].clone(),
        splited[13].clone(),
    ];

    (Some(line_sensor), Some(row))
}

fn _parse_squid(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &SQUID_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_sensor = object_dict_of_addresses_and_names
        .get(line_sensor)
        .unwrap_or(line_sensor)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        // splited[5].clone(),
        splited[6].clone(),
        splited[7].clone(),
        splited[8].clone(),
        splited[9].clone(),
        splited[10].clone(),
        splited[11].clone(),
        splited[12].clone().to_lowercase(),
        splited[13].clone(),
        splited[14].clone(),
        splited[15].clone(),
        splited[16].clone(),
    ];

    (Some(line_sensor), Some(row))
}

fn _parse_switch(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &SWITCH_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_switch     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_switch,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_switch = object_dict_of_addresses_and_names
        .get(line_switch)
        .unwrap_or(line_switch)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),
    ];

    (Some(line_switch), Some(row))
}

fn _parse_useraudit(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if !ln.contains("Successful login") && !ln.contains("User logged out") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern(ln, &USERAUDIT_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_sensor = object_dict_of_addresses_and_names
        .get(line_sensor)
        .unwrap_or(line_sensor)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        // line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),
    ];

    (Some(line_sensor), Some(row))
}

fn _parse_usernotice(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &USERNOTICE_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_sensor = object_dict_of_addresses_and_names
        .get(line_sensor)
        .unwrap_or(line_sensor)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        splited[5].clone(),
        splited[6].clone(),
        splited[7].clone(),
        splited[8].clone(),
        splited[9].clone(),
    ];

    (Some(line_sensor), Some(row))
}

fn _parse_userwarning(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &USERWARNING_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_sensor = object_dict_of_addresses_and_names
        .get(line_sensor)
        .unwrap_or(line_sensor)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        // line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),
    ];

    (Some(line_sensor), Some(row))
}

fn _parse_vmware(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &VMWARE_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_vmware     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_vmware,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_vmware = object_dict_of_addresses_and_names
        .get(line_vmware)
        .unwrap_or(line_vmware)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),
    ];

    (Some(line_vmware), Some(row))
}

fn _parse_vpnserver(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &VPNSERVER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    // let line_sensor     = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];
    // let rest            = &splited[5];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        splited[5].clone(),
        splited[6].clone(),
        splited[7].clone(),
        splited[8].clone(),
        splited[9].clone(),
        splited[10].clone(),
    ];

    (None, Some(row))
}

fn _parse_windowsserver(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if ln.contains("[dns]") || ln.contains("[dhcp]") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern(ln, &WINDOWSSERVER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_windowsserver = &splited[2];  // WindowsServer1 OR ***********
    let line_event_type    = &splited[3];  // (daemon/err), ...
    let line_alert_type    = &splited[4];  // [MSWinEventLog 1 N/A 2873106 Fri]
    let message            = splited[5].replace("\t", " ");

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_windowsserver,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }



    // START __prepare_more_variables__

    // event_id
    let message_parts: Vec<&str> = message.split_whitespace().collect();
    let event_id = if message_parts.len() > 4 {
        message_parts[4].to_string()  // '4625', 'N/A', ...
    } else {
        "".to_string()
    };

    // category
    let category = WindowsServerConfig::get_category_from_event_id(&event_id);

    // potential_criticality
    let potential_criticality = if let MYSQLValue::Dict(dict) = WindowsServerConfig::EVENT_IDS_AND_POTENTIAL_CRITICALITIES.value() {
        dict.get(&event_id).cloned().unwrap_or_else(|| "".to_string())
    } else {
        "".to_string()
    };

    // account_name, account_domain
    let (account_name, account_domain) = if let Some(account_matches) = _extract_matches_from_pattern(&message, &WS_AN_AD_PATTERN) {
        (account_matches[0].clone(), account_matches[1].clone())  // ('SYSTEM', 'NT AUTHORITY')
    } else {
        ("".to_string(), "".to_string())
    };

    // source workstation
    let source_workstation = if let Some(sw_matches) = _extract_matches_from_pattern(&message, &WS_SW_PATTERN) {
        sw_matches[0].clone()  // Windows7
    } else {
        "".to_string()
    };

    // END __prepare_more_variables__



    let line_windowsserver = object_dict_of_addresses_and_names
        .get(line_windowsserver)
        .unwrap_or(line_windowsserver)
        .clone();

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        // line_event_type,
        line_alert_type.clone(),
        message,
        event_id,
        category,
        potential_criticality,
        account_name,
        account_domain,
        source_workstation,
    ];

    (Some(line_windowsserver), Some(row))
}

static _PRECOMPUTED_VALUES: Lazy<HashMap<&'static str, (String, String, bool, Vec<String>)>> = Lazy::new(|| {
    let mut map = HashMap::new();

    let extract_config_values = |config_type: ConfigType| -> (String, String, bool, Vec<String>) {
        match config_type {
            ConfigType::Daemon => {
                let slug = if let MYSQLValue::Str(s) = DaemonConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = DaemonConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = DaemonConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = DaemonConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::DHCP => {
                let slug = if let MYSQLValue::Str(s) = DHCPConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = DHCPConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = DHCPConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = DHCPConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::DNS => {
                let slug = if let MYSQLValue::Str(s) = DNSConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = DNSConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = DNSConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = DNSConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::FilterLog => {
                let slug = if let MYSQLValue::Str(s) = FilterLogConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = FilterLogConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = FilterLogConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = FilterLogConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Router => {
                let slug = if let MYSQLValue::Str(s) = RouterConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = RouterConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = RouterConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = RouterConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::RouterBoard => {
                let slug = if let MYSQLValue::Str(s) = RouterBoardConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = RouterBoardConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = RouterBoardConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = RouterBoardConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Snort => {
                let slug = if let MYSQLValue::Str(s) = SnortConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = SnortConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = SnortConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = SnortConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Squid => {
                let slug = if let MYSQLValue::Str(s) = SquidConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = SquidConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = SquidConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = SquidConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::Switch => {
                let slug = if let MYSQLValue::Str(s) = SwitchConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = SwitchConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = SwitchConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = SwitchConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::UserAudit => {
                let slug = if let MYSQLValue::Str(s) = UserAuditConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = UserAuditConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = UserAuditConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = UserAuditConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::UserNotice => {
                let slug = if let MYSQLValue::Str(s) = UserNoticeConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = UserNoticeConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = UserNoticeConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = UserNoticeConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::UserWarning => {
                let slug = if let MYSQLValue::Str(s) = UserWarningConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = UserWarningConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = UserWarningConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = UserWarningConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::VMware => {
                let slug = if let MYSQLValue::Str(s) = VMwareConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = VMwareConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = VMwareConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = VMwareConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::VPNServer => {
                let slug = if let MYSQLValue::Str(s) = VPNServerConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = VPNServerConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = VPNServerConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = VPNServerConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
            ConfigType::WindowsServer => {
                let slug = if let MYSQLValue::Str(s) = WindowsServerConfig::SLUG.value() { s } else { String::new() };
                let filterby = if let MYSQLValue::Str(s) = WindowsServerConfig::FILTERBY.value() { s } else { String::new() };
                let filterby_is_in_alert_type = if let MYSQLValue::Bool(b) = WindowsServerConfig::FILTERBY_IS_IN_ALERT_TYPE.value() { b } else { false };
                let event_types = if let MYSQLValue::List(l) = WindowsServerConfig::EVENT_TYPES.value() { l } else { vec![] };
                (slug, filterby, filterby_is_in_alert_type, event_types)
            },
        }
    };

    let configs = [
        (ConfigType::Daemon, "daemon"),
        (ConfigType::DHCP, "dhcp"),
        (ConfigType::DNS, "dns"),
        (ConfigType::FilterLog, "filterlog"),
        (ConfigType::Router, "router"),
        (ConfigType::RouterBoard, "routerboard"),
        (ConfigType::Snort, "snort"),
        (ConfigType::Squid, "squid"),
        (ConfigType::Switch, "switch"),
        (ConfigType::UserAudit, "useraudit"),
        (ConfigType::UserNotice, "usernotice"),
        (ConfigType::UserWarning, "userwarning"),
        (ConfigType::VMware, "vmware"),
        (ConfigType::VPNServer, "vpnserver"),
        (ConfigType::WindowsServer, "windowsserver"),
    ];

    for (config_type, key) in configs {
        let values = extract_config_values(config_type);
        map.insert(key, values);
    }

    map
});

static _PARSERS: Lazy<HashMap<&'static str, fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>)>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("daemon",        _parse_daemon        as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("dhcp",          _parse_dhcp          as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("dns",           _parse_dns           as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    // map.insert("filterlog",     _parse_filterlog     as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("router",        _parse_router        as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("routerboard",   _parse_routerboard   as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("snort",         _parse_snort         as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("squid",         _parse_squid         as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("switch",        _parse_switch        as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("useraudit",     _parse_useraudit     as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("usernotice",    _parse_usernotice    as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("userwarning",   _parse_userwarning   as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("vmware",        _parse_vmware        as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("vpnserver",     _parse_vpnserver     as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map.insert("windowsserver", _parse_windowsserver as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));
    map
});

pub fn parse_ln(
    ln: &str,
    cls: ConfigType,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
) -> (Option<String>, Option<Vec<String>>) {
    if _is_invalid_ln(ln) {
        return (None, None);
    }

    let slug_key = match cls {
        ConfigType::Daemon => "daemon",
        ConfigType::DHCP => "dhcp",
        ConfigType::DNS => "dns",
        ConfigType::FilterLog => "filterlog",
        ConfigType::Router => "router",
        ConfigType::RouterBoard => "routerboard",
        ConfigType::Snort => "snort",
        ConfigType::Squid => "squid",
        ConfigType::Switch => "switch",
        ConfigType::UserAudit => "useraudit",
        ConfigType::UserNotice => "usernotice",
        ConfigType::UserWarning => "userwarning",
        ConfigType::VMware => "vmware",
        ConfigType::VPNServer => "vpnserver",
        ConfigType::WindowsServer => "windowsserver",
    };

    let (slug, filterby, filterby_is_in_alert_type, event_types) = match _PRECOMPUTED_VALUES.get(slug_key) {
        Some(values) => values,
        None => return (None, None),
    };

    if !filterby.is_empty() && !filterby_is_in_alert_type && !ln.contains(filterby) {
        return (None, None);
    }

    let func = match _PARSERS.get(slug.as_str()) {
        Some(f) => f,
        None => return (None, None),
    };

    func(
        ln,
        object_list_of_names_and_addresses,
        object_dict_of_addresses_and_names,
        event_types,
        *filterby_is_in_alert_type,
        filterby,
    )
}
